from openai import OpenAI

client = OpenAI(
    api_key="sk-8a2b5c9d-e1f3-4g7h-6i2j-k3l4m5n6o7p8", # 请替换成您的ModelScope Access Token
    base_url="http://8.137.120.199:6000/v1/chat"
)

extra_body = {"chat_template_kwargs": {"enable_thinking": False}}

response = client.chat.completions.create(
    model="qwen14b", # ModleScope Model-Id
    messages=[
        {
            'role': 'system',
            'content': 'You are a helpful assistant.'
        },
        {
            'role': 'user',
            'content': '用python写一下快排'
        }
    ],
    stream=True,
    extra_body=extra_body
)

for chunk in response:
    print(chunk.choices[0].delta.content, end='', flush=True)

# from openai import OpenAI

# client = OpenAI(
#     base_url='http://8.137.120.199:6000/v1/chat',
#     api_key='sk-8a2b5c9d-e1f3-4g7h-6i2j-k3l4m5n6o7p8', # ModelScope Token
# )

# # set extra_body for thinking control
# extra_body = {
#     # enable thinking, set to False to disable
#     "enable_thinking": True,
#     # use thinking_budget to contorl num of tokens used for thinking
#     # "thinking_budget": 4096
# }

# response = client.chat.completions.create(
#     model='qwen14b', # ModelScope Model-Id, required
#     messages=[
#         {
#           'role': 'user',
#           'content': '什么是快乐星球'
#         }
#     ],
#     stream=True,
#     extra_body=extra_body
# )
# done_thinking = False
# for chunk in response:
#     thinking_chunk = chunk.choices[0].delta.reasoning_content
#     answer_chunk = chunk.choices[0].delta.content
#     if thinking_chunk != '':
#         print(thinking_chunk, end='', flush=True)
#     elif answer_chunk != '':
#         if not done_thinking:
#             print('\n\n === Final Answer ===\n')
#             done_thinking = True
#         print(answer_chunk, end='', flush=True)