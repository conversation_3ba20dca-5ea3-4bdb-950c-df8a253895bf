"""
基于分类的问答存储管理器
按照category将问答对分别存储到不同的文件夹中
"""

import asyncio
import json
import os
import time
import uuid
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from dataclasses import dataclass, field
import logging

from core.rag.base import BaseVectorStorage
from nano_vectordb import NanoVectorDB
from .qa_vector_storage import QAPair
from .qa_concurrency_manager import QAConcurrencyManager

logger = logging.getLogger(__name__)


class CategoryQAStorage:
    """基于分类的问答存储管理器"""
    
    def __init__(self, namespace: str, workspace: str, global_config: dict, embedding_func, similarity_threshold: float = 0.98):
        self.namespace = namespace
        self.workspace = workspace
        self.global_config = global_config
        self.embedding_func = embedding_func
        self.similarity_threshold = similarity_threshold
        
        # 设置基础存储路径
        # 优先使用 qa_storage_dir 配置，如果没有则使用 working_dir/Q_A_Base
        qa_storage_dir = global_config.get("qa_storage_dir")
        if qa_storage_dir:
            self.base_storage_path = qa_storage_dir
        else:
            working_dir = global_config["working_dir"]
            # 如果working_dir已经以Q_A_Base结尾，直接使用，否则添加Q_A_Base
            if working_dir.endswith("Q_A_Base"):
                self.base_storage_path = working_dir
            else:
                self.base_storage_path = os.path.join(working_dir, "Q_A_Base")
        os.makedirs(self.base_storage_path, exist_ok=True)
        
        # 分类存储管理
        self.category_storages: Dict[str, 'QAVectorStorage'] = {}
        self.qa_pairs: Dict[str, QAPair] = {}  # 全局问答对索引
        self.initialized = False
    
    async def initialize(self) -> bool:
        """初始化存储"""
        try:
            # 扫描现有的分类文件夹
            await self._scan_existing_categories()
            self.initialized = True
            logger.info(f"Category QA Storage initialized with {len(self.category_storages)} categories")
            return True
        except Exception as e:
            logger.error(f"Error initializing Category QA Storage: {e}")
            return False
    
    async def _scan_existing_categories(self):
        """扫描现有的分类文件夹"""
        if not os.path.exists(self.base_storage_path):
            return
        
        for item in os.listdir(self.base_storage_path):
            category_path = os.path.join(self.base_storage_path, item)
            if os.path.isdir(category_path):
                # 检查是否有问答对文件
                qa_file = os.path.join(category_path, f"qa_pairs_{self.namespace}.json")
                if os.path.exists(qa_file):
                    await self._load_category_storage(item)
    
    async def _load_category_storage(self, category: str):
        """加载特定分类的存储"""
        try:
            from .qa_vector_storage import QAVectorStorage

            # 创建分类专用的配置，working_dir指向分类文件夹
            category_config = self.global_config.copy()
            category_config["working_dir"] = os.path.join(self.base_storage_path, category)

            # 创建分类专用的存储
            storage = QAVectorStorage(
                namespace=self.namespace,
                workspace="",  # 空字符串，因为working_dir已经指向分类文件夹
                global_config=category_config,
                embedding_func=self.embedding_func,
                similarity_threshold=self.similarity_threshold
            )
            
            # 初始化存储
            success = await storage.initialize()
            if success:
                self.category_storages[category] = storage
                # 将问答对添加到全局索引
                for qa_id, qa_pair in storage.qa_pairs.items():
                    self.qa_pairs[qa_id] = qa_pair
                logger.info(f"Loaded category '{category}' with {len(storage.qa_pairs)} QA pairs")
            else:
                logger.error(f"Failed to initialize storage for category '{category}'")
                
        except Exception as e:
            logger.error(f"Error loading category storage '{category}': {e}")
    
    async def _get_or_create_category_storage(self, category: str):
        """获取或创建分类存储（支持并发安全）"""
        # 双重检查锁定模式：先检查是否已存在
        if category in self.category_storages:
            storage = self.category_storages[category]
            # 验证存储的有效性
            if hasattr(storage, 'qa_pairs') and hasattr(storage, 'initialized'):
                return storage
            else:
                logger.warning(f"Category '{category}' storage is corrupted, will recreate")
                # 移除无效的存储
                del self.category_storages[category]

        # 使用全局锁确保分类存储的创建是原子的
        # 这防止多个并发请求同时创建同一个分类的存储
        async with QAConcurrencyManager.get_global_qa_lock("create_category", enable_logging=True):
            # 再次检查（双重检查锁定模式）
            if category in self.category_storages:
                storage = self.category_storages[category]
                if hasattr(storage, 'qa_pairs') and hasattr(storage, 'initialized'):
                    logger.debug(f"Category '{category}' storage created by another thread")
                    return storage
                else:
                    # 清理无效存储
                    del self.category_storages[category]

            # 创建新的分类存储
            try:
                await self._load_category_storage(category)
                storage = self.category_storages.get(category)
                if storage:
                    logger.info(f"Successfully created new storage for category '{category}'")
                else:
                    logger.error(f"Failed to load storage for category '{category}'")

                return storage
            except Exception as e:
                logger.error(f"Error creating storage for category '{category}': {e}")
                import traceback
                logger.debug(f"Create category storage error traceback: {traceback.format_exc()}")
                return None
    
    async def add_qa_pair(self, question: str, answer: str, **kwargs) -> Optional[str]:
        """添加问答对到指定分类（支持并发安全）"""
        category = kwargs.get('category', 'general')

        # 使用分类级创建锁，确保同一分类的创建操作和删除操作互斥
        async with QAConcurrencyManager.get_category_lock(category, "create", enable_logging=True):
            try:
                # 在锁内检查分类是否仍然存在（防止在等待锁期间被删除）
                if category in self.category_storages:
                    # 检查存储是否仍然有效
                    storage = self.category_storages[category]
                    if not hasattr(storage, 'qa_pairs'):
                        logger.warning(f"Category '{category}' storage is invalid, recreating...")
                        storage = None
                    else:
                        logger.debug(f"Using existing storage for category '{category}'")
                else:
                    storage = None

                # 获取或创建分类存储
                if not storage:
                    storage = await self._get_or_create_category_storage(category)
                    if not storage:
                        logger.error(f"Failed to get storage for category '{category}'")
                        return None

                # 添加问答对
                qa_id = await storage.add_qa_pair(question, answer, **kwargs)
                if qa_id:
                    # 添加到全局索引
                    qa_pair = storage.qa_pairs.get(qa_id)
                    if qa_pair:
                        self.qa_pairs[qa_id] = qa_pair

                    # 保存数据
                    await storage.index_done_callback()
                    logger.info(f"Successfully added QA pair {qa_id} to category '{category}'")
                else:
                    logger.warning(f"Failed to add QA pair to category '{category}': storage returned None")

                return qa_id

            except Exception as e:
                logger.error(f"Error adding QA pair to category '{category}': {e}")
                import traceback
                logger.debug(f"Add QA pair error traceback: {traceback.format_exc()}")
                return None
    
    async def add_qa_pairs_batch(self, qa_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量添加问答对"""
        added_ids = []
        skipped_duplicates = []
        failed_items = []

        # 按分类分组
        category_groups = {}
        for qa_item in qa_data:
            category = qa_item.get('category', 'general')
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(qa_item)

        # 获取所有涉及的分类，并按字母顺序排序以避免死锁
        categories = sorted(category_groups.keys())

        # 使用多分类锁确保批量操作的原子性
        async with QAConcurrencyManager.get_multiple_category_locks(categories, "create", enable_logging=True):
            # 为每个分类批量添加
            for category in categories:
                items = category_groups[category]
                try:
                    storage = await self._get_or_create_category_storage(category)
                    if storage:
                        # 调用存储的批量添加方法（现在返回字典）
                        result = await storage.add_qa_pairs_batch(items)

                        # 处理结果
                        if isinstance(result, dict):
                            # 新的返回格式
                            batch_ids = result.get("added_ids", [])
                            added_ids.extend(batch_ids)
                            skipped_duplicates.extend(result.get("skipped_duplicates", []))
                            failed_items.extend(result.get("failed_items", []))
                        else:
                            # 旧的返回格式（向后兼容）
                            batch_ids = result if isinstance(result, list) else []
                            added_ids.extend(batch_ids)

                        # 更新全局索引
                        for qa_id in batch_ids:
                            qa_pair = storage.qa_pairs.get(qa_id)
                            if qa_pair:
                                self.qa_pairs[qa_id] = qa_pair

                        # 保存数据
                        await storage.index_done_callback()
                        logger.info(f"Successfully batch added {len(batch_ids)} QA pairs to category '{category}'")
                    else:
                        # 如果无法获取存储，将所有项目标记为失败
                        for item in items:
                            failed_items.append({
                                **item,
                                "error": f"Failed to get storage for category '{category}'"
                            })
                        logger.error(f"Failed to get storage for category '{category}'")

                except Exception as e:
                    logger.error(f"Error batch adding QA pairs to category '{category}': {e}")
                    import traceback
                    logger.debug(f"Batch add error traceback: {traceback.format_exc()}")

                    # 将该分类的所有项目标记为失败
                    for item in items:
                        failed_items.append({
                            **item,
                            "error": str(e)
                        })

        return {
            "added_ids": added_ids,
            "added_count": len(added_ids),
            "skipped_duplicates": skipped_duplicates,
            "skipped_count": len(skipped_duplicates),
            "failed_items": failed_items,
            "failed_count": len(failed_items)
        }
    
    async def query_qa(self, question: str, top_k: int = 1, min_similarity: Optional[float] = None, category: Optional[str] = None, better_than_threshold: Optional[float] = None) -> Dict[str, Any]:
        """查询问答（支持并发安全）"""
        if not self.qa_pairs:
            return {
                "success": True,
                "found": False,
                "message": "No QA pairs available"
            }

        try:
            if category:
                # 在指定分类中查询，使用读锁
                async with QAConcurrencyManager.get_category_lock(category, "query", enable_logging=False):
                    storage = self.category_storages.get(category)
                    if not storage:
                        return {
                            "success": True,
                            "found": False,
                            "message": f"Category '{category}' not found"
                        }
                    return await storage.query_qa(question, top_k, min_similarity, None, better_than_threshold)
            else:
                # 全局查询：在所有分类中查询并合并结果
                all_results = []

                for cat_name, storage in self.category_storages.items():
                    try:
                        # 使用很低的阈值获取所有可能的结果
                        result = await storage.query_qa(question, top_k=10, min_similarity=0.0, category=None, better_than_threshold=None)

                        # 收集所有结果，不管是否找到
                        if "all_results" in result and result["all_results"]:
                            all_results.extend(result["all_results"])
                        elif result.get("found", False):
                            # 单个结果
                            all_results.append({
                                "qa_id": result.get("qa_id"),
                                "question": result.get("question"),
                                "answer": result.get("answer"),
                                "category": result.get("category"),
                                "confidence": result.get("confidence"),
                                "similarity": result.get("similarity")
                            })
                    except Exception as e:
                        logger.error(f"Error querying category '{cat_name}': {e}")
                        continue
                
                if not all_results:
                    return {
                        "success": True,
                        "found": False,
                        "message": "No matching QA pairs found in any category"
                    }
                
                # 按相似度排序
                all_results.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                
                # 应用相似度阈值
                similarity_threshold = self.similarity_threshold if min_similarity is None else float(min_similarity)
                filtered_results = [r for r in all_results if r.get("similarity", 0) >= similarity_threshold]

                if not filtered_results:
                    best_similarity = all_results[0].get("similarity", 0) if all_results else 0
                    return {
                        "success": True,
                        "found": False,
                        "message": f"No QA pair found with similarity >= {similarity_threshold}",
                        "best_similarity": float(best_similarity)
                    }
                
                # 返回最佳结果
                best_result = filtered_results[0]
                return {
                    "success": True,
                    "found": True,
                    "qa_id": best_result.get("qa_id"),
                    "question": best_result.get("question"),
                    "answer": best_result.get("answer"),
                    "category": best_result.get("category"),
                    "confidence": best_result.get("confidence"),
                    "similarity": best_result.get("similarity"),
                    "all_results": filtered_results[:top_k]
                }
                
        except Exception as e:
            logger.error(f"Error querying QA: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def list_qa_pairs(self, category: str = None, min_confidence: float = None) -> List[Dict[str, Any]]:
        """列出问答对"""
        results = []
        
        if category:
            # 列出指定分类的问答对
            storage = self.category_storages.get(category)
            if storage:
                return storage.list_qa_pairs(None, min_confidence)
        else:
            # 列出所有分类的问答对
            for qa_pair in self.qa_pairs.values():
                # 应用过滤条件
                if min_confidence and qa_pair.confidence < min_confidence:
                    continue
                
                results.append({
                    "id": qa_pair.id,
                    "question": qa_pair.question,
                    "answer": qa_pair.answer,
                    "category": qa_pair.category,
                    "confidence": qa_pair.confidence,
                    "keywords": qa_pair.keywords,
                    "source": qa_pair.source,
                    "created_at": qa_pair.created_at,
                    "updated_at": qa_pair.updated_at
                })
        
        return results
    
    def get_categories(self) -> List[str]:
        """获取所有分类列表"""
        return list(self.category_storages.keys())
    
    def get_category_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取各分类的统计信息"""
        stats = {}
        for category, storage in self.category_storages.items():
            stats[category] = {
                "total_pairs": len(storage.qa_pairs),
                "storage_path": storage.qa_storage_file
            }
        return stats

    async def delete_category(self, category: str) -> Dict[str, Any]:
        """删除特定分类的所有问答数据和对应文件夹（支持并发安全）"""
        # 使用分类级删除锁，确保同一分类的删除操作串行执行
        async with QAConcurrencyManager.get_category_lock(category, "delete", enable_logging=True):
            try:
                # 双重检查：在获得锁后再次检查分类是否存在
                # 防止多个并发删除请求同时进入
                if category not in self.category_storages:
                    # 检查是否存在分类文件夹但未加载
                    category_path = os.path.join(self.base_storage_path, category)
                    if os.path.exists(category_path) and os.path.isdir(category_path):
                        # 直接删除文件夹
                        import shutil
                        try:
                            shutil.rmtree(category_path)
                            logger.info(f"Deleted unloaded category folder '{category}'")
                            return {
                                "success": True,
                                "message": f"成功删除分类 '{category}' 文件夹",
                                "deleted_count": 0,
                                "folder_deleted": True
                            }
                        except Exception as folder_error:
                            logger.error(f"Failed to delete unloaded category folder {category_path}: {folder_error}")
                            return {
                                "success": False,
                                "message": f"删除分类文件夹失败: {str(folder_error)}",
                                "deleted_count": 0,
                                "folder_deleted": False
                            }
                    else:
                        return {
                            "success": False,
                            "message": f"分类 '{category}' 不存在",
                            "deleted_count": 0,
                            "folder_deleted": False
                        }

                storage = self.category_storages[category]
                deleted_count = len(storage.qa_pairs)
                category_path = os.path.join(self.base_storage_path, category)

                # 记录开始删除
                logger.info(f"Starting deletion of category '{category}' with {deleted_count} QA pairs")

                # 从全局索引中删除该分类的所有问答对
                qa_ids_to_remove = list(storage.qa_pairs.keys())
                for qa_id in qa_ids_to_remove:
                    if qa_id in self.qa_pairs:
                        del self.qa_pairs[qa_id]

                # 清空该分类的存储（删除文件）
                await storage.drop()

                # 从分类存储中移除
                del self.category_storages[category]

                # 删除整个分类文件夹
                folder_deleted = False
                if os.path.exists(category_path) and os.path.isdir(category_path):
                    try:
                        import shutil
                        shutil.rmtree(category_path)
                        folder_deleted = True
                        logger.info(f"Successfully deleted category folder: {category_path}")
                    except Exception as folder_error:
                        logger.warning(f"Failed to delete category folder {category_path}: {folder_error}")

                logger.info(f"Successfully deleted category '{category}' with {deleted_count} QA pairs, folder deleted: {folder_deleted}")

                return {
                    "success": True,
                    "message": f"成功删除分类 '{category}' 及其 {deleted_count} 个问答对" +
                              (f"，文件夹已删除" if folder_deleted else f"，文件夹删除失败"),
                    "deleted_count": deleted_count,
                    "folder_deleted": folder_deleted
                }

            except Exception as e:
                logger.error(f"Error deleting category '{category}': {e}")
                import traceback
                logger.debug(f"Delete category error traceback: {traceback.format_exc()}")
                return {
                    "success": False,
                    "message": f"删除分类失败: {str(e)}",
                    "deleted_count": 0,
                    "folder_deleted": False
                }

    async def delete_qa_pairs_by_ids(self, qa_ids: List[str]) -> Dict[str, Any]:
        """根据ID列表删除问答对"""
        try:
            deleted_count = 0
            not_found_ids = []

            for qa_id in qa_ids:
                # 从全局索引中查找
                if qa_id in self.qa_pairs:
                    qa_pair = self.qa_pairs[qa_id]
                    category = qa_pair.category

                    # 从对应分类存储中删除
                    if category in self.category_storages:
                        storage = self.category_storages[category]
                        if qa_id in storage.qa_pairs:
                            # 从向量数据库中删除
                            await storage.delete([qa_id])
                            deleted_count += 1

                    # 从全局索引中删除
                    del self.qa_pairs[qa_id]
                else:
                    not_found_ids.append(qa_id)

            # 保存所有受影响的分类存储
            affected_categories = set()
            for qa_id in qa_ids:
                if qa_id not in not_found_ids:
                    # 通过遍历找到受影响的分类
                    for category, storage in self.category_storages.items():
                        if any(qa_id in storage.qa_pairs for qa_id in qa_ids):
                            affected_categories.add(category)

            for category in affected_categories:
                await self.category_storages[category].index_done_callback()

            result_message = f"成功删除 {deleted_count} 个问答对"
            if not_found_ids:
                result_message += f"，{len(not_found_ids)} 个ID未找到"

            logger.info(f"Deleted {deleted_count} QA pairs, {len(not_found_ids)} not found")

            return {
                "success": True,
                "message": result_message,
                "deleted_count": deleted_count,
                "not_found_count": len(not_found_ids),
                "not_found_ids": not_found_ids
            }

        except Exception as e:
            logger.error(f"Error deleting QA pairs by IDs: {e}")
            return {
                "success": False,
                "message": f"删除问答对失败: {str(e)}",
                "deleted_count": 0,
                "not_found_count": 0,
                "not_found_ids": []
            }

    async def index_done_callback(self):
        """索引完成回调，保存所有分类的数据"""
        try:
            for category, storage in self.category_storages.items():
                await storage.index_done_callback()
            logger.info("All category storages saved successfully")
        except Exception as e:
            logger.error(f"Error in index_done_callback: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_pairs = len(self.qa_pairs)
        categories_count = len(self.category_storages)

        category_breakdown = {}
        total_confidence = 0
        for category, storage in self.category_storages.items():
            category_breakdown[category] = len(storage.qa_pairs)
            # 计算该分类的总置信度
            for qa_pair in storage.qa_pairs.values():
                total_confidence += qa_pair.confidence

        avg_confidence = total_confidence / total_pairs if total_pairs > 0 else 0

        # 返回与 QAVectorStorage 兼容的数据结构
        return {
            "storage_stats": {
                "total_pairs": total_pairs,
                "categories": category_breakdown,
                "average_confidence": avg_confidence,
                "similarity_threshold": self.similarity_threshold,
                "vector_index_size": total_pairs,
                "embedding_dim": getattr(self.embedding_func, 'embedding_dim', 0),
                "query_stats": {
                    "total_queries": 0,  # 可以添加查询统计
                    "successful_queries": 0,
                    "avg_response_time": 0.0
                }
            },
            # 保留原有的字段以保持向后兼容
            "total_qa_pairs": total_pairs,
            "total_categories": categories_count,
            "category_breakdown": category_breakdown,
            "storage_type": "CategoryQAStorage"
        }
